#!/usr/bin/env node

/**
 * Performance Monitoring Script
 * Monitors database performance and provides optimization recommendations
 */

import { query, getPoolStats } from '../src/lib/local-db.js'
import { logger } from '../src/lib/logger.js'

async function checkSlowQueries() {
  try {
    // Check for slow queries in the last hour
    const result = await query(`
      SELECT 
        query,
        calls,
        total_time,
        mean_time,
        max_time,
        rows
      FROM pg_stat_statements 
      WHERE mean_time > 1000  -- Queries taking more than 1 second on average
      ORDER BY mean_time DESC 
      LIMIT 10
    `)

    if (result.rows.length > 0) {
      logger.warn('Slow queries detected', {
        count: result.rows.length,
        slowestQuery: {
          query: result.rows[0].query.substring(0, 100) + '...',
          meanTime: Math.round(result.rows[0].mean_time),
          calls: result.rows[0].calls
        }
      })
    }

    return result.rows
  } catch (error) {
    logger.warn('Could not check slow queries (pg_stat_statements may not be enabled)', { error: error.message })
    return []
  }
}

async function checkIndexUsage() {
  try {
    // Check for unused indexes
    const unusedIndexes = await query(`
      SELECT 
        schemaname,
        tablename,
        indexname,
        idx_tup_read,
        idx_tup_fetch
      FROM pg_stat_user_indexes 
      WHERE idx_tup_read = 0 AND idx_tup_fetch = 0
      ORDER BY schemaname, tablename, indexname
    `)

    // Check for missing indexes on foreign keys
    const missingIndexes = await query(`
      SELECT 
        c.conrelid::regclass AS table_name,
        a.attname AS column_name,
        c.confrelid::regclass AS referenced_table
      FROM pg_constraint c
      JOIN pg_attribute a ON a.attnum = ANY(c.conkey) AND a.attrelid = c.conrelid
      WHERE c.contype = 'f'
      AND NOT EXISTS (
        SELECT 1 FROM pg_index i 
        WHERE i.indrelid = c.conrelid 
        AND a.attnum = ANY(i.indkey)
      )
    `)

    return {
      unusedIndexes: unusedIndexes.rows,
      missingIndexes: missingIndexes.rows
    }
  } catch (error) {
    logger.error('Error checking index usage', { error })
    return { unusedIndexes: [], missingIndexes: [] }
  }
}

async function checkCacheHitRatio() {
  try {
    const result = await query(`
      SELECT 
        sum(heap_blks_read) as heap_read,
        sum(heap_blks_hit) as heap_hit,
        sum(heap_blks_hit) / (sum(heap_blks_hit) + sum(heap_blks_read)) * 100 as cache_hit_ratio
      FROM pg_statio_user_tables
    `)

    const cacheHitRatio = result.rows[0]?.cache_hit_ratio || 0
    
    if (cacheHitRatio < 95) {
      logger.warn('Low cache hit ratio detected', { 
        cacheHitRatio: Math.round(cacheHitRatio * 100) / 100 
      })
    }

    return cacheHitRatio
  } catch (error) {
    logger.error('Error checking cache hit ratio', { error })
    return 0
  }
}

async function checkTableSizes() {
  try {
    const result = await query(`
      SELECT 
        schemaname,
        tablename,
        pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size,
        pg_total_relation_size(schemaname||'.'||tablename) as size_bytes
      FROM pg_tables 
      WHERE schemaname = 'public'
      ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC
      LIMIT 10
    `)

    return result.rows
  } catch (error) {
    logger.error('Error checking table sizes', { error })
    return []
  }
}

async function checkConnectionStats() {
  try {
    const result = await query(`
      SELECT 
        state,
        count(*) as connection_count
      FROM pg_stat_activity 
      WHERE datname = current_database()
      GROUP BY state
    `)

    return result.rows
  } catch (error) {
    logger.error('Error checking connection stats', { error })
    return []
  }
}

async function generateReport() {
  try {
    logger.info('Starting performance monitoring report')

    // Get pool statistics
    const poolStats = getPoolStats()
    
    // Check various performance metrics
    const [slowQueries, indexUsage, cacheHitRatio, tableSizes, connectionStats] = await Promise.all([
      checkSlowQueries(),
      checkIndexUsage(),
      checkCacheHitRatio(),
      checkTableSizes(),
      checkConnectionStats()
    ])

    const report = {
      timestamp: new Date().toISOString(),
      poolStats,
      cacheHitRatio: Math.round(cacheHitRatio * 100) / 100,
      slowQueriesCount: slowQueries.length,
      unusedIndexesCount: indexUsage.unusedIndexes.length,
      missingIndexesCount: indexUsage.missingIndexes.length,
      largestTables: tableSizes.slice(0, 5),
      connectionStats,
      recommendations: []
    }

    // Generate recommendations
    if (cacheHitRatio < 95) {
      report.recommendations.push('Consider increasing shared_buffers in PostgreSQL configuration')
    }

    if (slowQueries.length > 0) {
      report.recommendations.push(`${slowQueries.length} slow queries detected - review query performance`)
    }

    if (indexUsage.unusedIndexes.length > 5) {
      report.recommendations.push(`${indexUsage.unusedIndexes.length} unused indexes found - consider removing them`)
    }

    if (indexUsage.missingIndexes.length > 0) {
      report.recommendations.push(`${indexUsage.missingIndexes.length} foreign keys without indexes found`)
    }

    if (poolStats.waitingCount > 0) {
      report.recommendations.push('Connection pool has waiting connections - consider increasing pool size')
    }

    logger.info('Performance monitoring report', report)

    return report
  } catch (error) {
    logger.error('Error generating performance report', { error })
    return null
  }
}

async function main() {
  try {
    const report = await generateReport()
    
    if (report && report.recommendations.length > 0) {
      console.log('\n🔍 Performance Recommendations:')
      report.recommendations.forEach((rec, index) => {
        console.log(`${index + 1}. ${rec}`)
      })
    } else {
      console.log('\n✅ No performance issues detected')
    }
    
  } catch (error) {
    logger.error('Performance monitoring failed', { error })
    process.exit(1)
  }
}

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main()
}
