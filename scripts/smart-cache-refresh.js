#!/usr/bin/env node

/**
 * Smart Cache Refresh Script
 * Intelligently refreshes materialized views based on data changes
 */

import { query, getPoolStats } from '../src/lib/local-db.js'
import { logger } from '../src/lib/logger.js'

async function checkDataChanges() {
  try {
    // Check when materialized views were last refreshed
    const lastRefreshResult = await query(`
      SELECT 
        event_description,
        created_at,
        EXTRACT(EPOCH FROM (NOW() - created_at)) / 60 as minutes_ago
      FROM activity_log 
      WHERE event_type = 'cache_refresh' 
      ORDER BY created_at DESC 
      LIMIT 1
    `)

    const lastRefresh = lastRefreshResult.rows[0]
    const minutesSinceRefresh = lastRefresh ? lastRefresh.minutes_ago : 999999

    // Check for recent data changes
    const changesResult = await query(`
      SELECT 
        COUNT(*) as recent_changes
      FROM (
        SELECT created_at FROM companies WHERE created_at > NOW() - INTERVAL '5 minutes'
        UNION ALL
        SELECT created_at FROM company_benefits WHERE created_at > NOW() - INTERVAL '5 minutes'
        UNION ALL
        SELECT created_at FROM benefits WHERE created_at > NOW() - INTERVAL '5 minutes'
      ) changes
    `)

    const recentChanges = parseInt(changesResult.rows[0].recent_changes)

    logger.info('Cache refresh analysis', {
      minutesSinceRefresh: Math.round(minutesSinceRefresh),
      recentChanges,
      shouldRefresh: recentChanges > 0 || minutesSinceRefresh > 30
    })

    return {
      shouldRefresh: recentChanges > 0 || minutesSinceRefresh > 30,
      recentChanges,
      minutesSinceRefresh
    }
  } catch (error) {
    logger.error('Error checking data changes', { error })
    return { shouldRefresh: true, recentChanges: 0, minutesSinceRefresh: 999 }
  }
}

async function refreshMaterializedViews() {
  try {
    logger.info('Starting materialized view refresh')
    
    const startTime = Date.now()
    
    // Refresh views concurrently for better performance
    await query('REFRESH MATERIALIZED VIEW CONCURRENTLY companies_with_benefits_cache')
    logger.info('Refreshed companies_with_benefits_cache')
    
    await query('REFRESH MATERIALIZED VIEW CONCURRENTLY benefits_with_categories_cache')
    logger.info('Refreshed benefits_with_categories_cache')
    
    const duration = Date.now() - startTime
    
    // Log the refresh
    await query(`
      INSERT INTO activity_log (event_type, event_description, created_at)
      VALUES ('cache_refresh', $1, NOW())
    `, [`Materialized views refreshed in ${duration}ms`])
    
    logger.info('Materialized view refresh completed', { duration })
    
    return true
  } catch (error) {
    logger.error('Error refreshing materialized views', { error })
    return false
  }
}

async function cleanupExpiredCache() {
  try {
    const result = await query('SELECT cleanup_expired_cache()')
    const cleanedCount = result.rows[0]?.cleanup_expired_cache || 0
    
    if (cleanedCount > 0) {
      logger.info('Cleaned up expired cache entries', { cleanedCount })
    }
    
    return cleanedCount
  } catch (error) {
    logger.error('Error cleaning up expired cache', { error })
    return 0
  }
}

async function main() {
  try {
    logger.info('Starting smart cache refresh')
    
    // Check database pool health
    const poolStats = getPoolStats()
    logger.info('Database pool status', poolStats)
    
    // Check if refresh is needed
    const analysis = await checkDataChanges()
    
    if (analysis.shouldRefresh) {
      logger.info('Cache refresh needed, proceeding...')
      
      // Clean up expired cache first
      await cleanupExpiredCache()
      
      // Refresh materialized views
      const success = await refreshMaterializedViews()
      
      if (success) {
        logger.info('Smart cache refresh completed successfully')
      } else {
        logger.error('Smart cache refresh failed')
        process.exit(1)
      }
    } else {
      logger.info('Cache refresh not needed, skipping')
    }
    
  } catch (error) {
    logger.error('Smart cache refresh failed', { error })
    process.exit(1)
  }
}

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main()
}
