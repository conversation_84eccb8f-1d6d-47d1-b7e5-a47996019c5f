version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15
    container_name: benefitlens-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: benefitlens
      POSTGRES_USER: benefitlens_user
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/postgresql.conf:/etc/postgresql/postgresql.conf:ro
      # - ./database/init:/docker-entrypoint-initdb.d
    command: ["postgres", "-c", "config_file=/etc/postgresql/postgresql.conf"]
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U benefitlens_user -d benefitlens"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - benefitlens-network

  # BenefitLens Application
  app:
    image: rgarcia89/benefitlens:latest
    container_name: benefitlens-app
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - DATABASE_URL=postgresql://${POSTGRES_USER}:${POSTGRES_PASSWORD}@postgres:5432/${POSTGRES_DB}
      - CACHE_TYPE=postgresql
      - NODE_ENV=production
      - NEXT_PUBLIC_APP_URL=${NEXT_PUBLIC_APP_URL}
      - APP_URL=${NEXT_PUBLIC_APP_URL}
      - SESSION_SECRET=${SESSION_SECRET}
      - USE_LOCAL_AUTH=${USE_LOCAL_AUTH}
      - FROM_EMAIL=${FROM_EMAIL}
      - SMTP_HOST=${SMTP_HOST}
      - SMTP_PORT=${SMTP_PORT}
      - SMTP_USER=${SMTP_USER}
      - SMTP_PASS=${SMTP_PASS}
      - LOG_LEVEL=${LOG_LEVEL}
    depends_on:
      postgres:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - benefitlens-network
      - edge # For Caddy proxy

  # Adminer for database management
  adminer:
    image: adminer:latest
    container_name: benefitlens-adminer
    restart: unless-stopped
    ports:
      - "8080:8080"
    environment:
      ADMINER_DEFAULT_SERVER: postgres
    depends_on:
      - postgres
    networks:
      - benefitlens-network

  # # Watchtower for automatic container updates (handled by github action!)
  # watchtower:
  #   image: containrrr/watchtower
  #   container_name: benefitlens-watchtower
  #   restart: unless-stopped
  #   volumes:
  #     - /var/run/docker.sock:/var/run/docker.sock
  #   environment:
  #     - WATCHTOWER_CLEANUP=true
  #     - WATCHTOWER_POLL_INTERVAL=300  # Check every 5 minutes
  #     - WATCHTOWER_INCLUDE_STOPPED=true
  #     - WATCHTOWER_REVIVE_STOPPED=false
  #   command: benefitlens-app
  #   networks:
  #     - benefitlens-network

  # # Portainer for container management (optional)
  # portainer:
  #   image: portainer/portainer-ce:latest
  #   container_name: benefitlens-portainer
  #   restart: unless-stopped
  #   ports:
  #     - "9000:9000"
  #   volumes:
  #     - /var/run/docker.sock:/var/run/docker.sock
  #     - portainer_data:/data
  #   networks:
  #     - benefitlens-network

volumes:
  postgres_data:
  portainer_data:

networks:
  benefitlens-network:
    driver: bridge
  edge:
    external: true # Assuming 'edge' network is already created