-- Migration: Performance optimization indexes
-- Date: 2025-09-05
-- Description: Add additional indexes to optimize frequently used query patterns

-- Composite index for company search with filters
-- Optimizes the main company listing query with multiple filters
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_companies_search_composite
ON companies (industry, size, created_at DESC)
WHERE industry IS NOT NULL AND size IS NOT NULL;

-- Full-text search index for company names and descriptions
-- Improves search performance significantly
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_companies_fulltext_search
ON companies USING gin(to_tsvector('english', name || ' ' || COALESCE(description, '')));

-- Composite index for company benefits filtering
-- Optimizes queries that filter companies by specific benefits
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_company_benefits_filter_composite
ON company_benefits (benefit_id, is_verified, company_id);

-- Index for location-based company searches
-- Optimizes location filtering in company queries
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_company_locations_search_composite
ON company_locations (city, country, company_id)
WHERE city IS NOT NULL AND country IS NOT NULL;

-- Index for analytics queries
-- Optimizes company ranking calculations
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_company_analytics_ranking
ON company_analytics_summary (company_id, date DESC, page_views, benefit_interactions)
WHERE date >= CURRENT_DATE - INTERVAL '30 days';

-- Index for user session cleanup
-- Optimizes session cleanup operations
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_sessions_cleanup
ON user_sessions (expires_at, created_at)
WHERE expires_at < NOW();

-- Index for search query analytics
-- Optimizes search analytics queries
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_search_queries_analytics
ON search_queries (created_at DESC, query_text)
WHERE created_at >= CURRENT_DATE - INTERVAL '30 days';

-- Partial index for active company benefits
-- Optimizes queries that only look at verified benefits
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_company_benefits_verified_only
ON company_benefits (company_id, benefit_id)
WHERE is_verified = true;

-- Index for benefit verification queries
-- Optimizes benefit verification status checks
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_benefit_verifications_composite
ON benefit_verifications (company_benefit_id, status, created_at DESC);

-- Index for user benefit rankings
-- Optimizes user-specific benefit ranking queries
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_benefit_rankings_composite
ON user_benefit_rankings (user_id, ranking ASC, updated_at DESC);

-- Update migration log
INSERT INTO migration_log (id, migration_name, description) VALUES
(30, '030-performance-optimization-indexes', 'Added performance optimization indexes for frequently used query patterns')
ON CONFLICT (migration_name) DO NOTHING;
