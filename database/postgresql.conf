# PostgreSQL Performance Configuration for BenefitLens
# Optimized for a typical web application workload

# Memory Configuration
shared_buffers = 256MB                    # 25% of available RAM (adjust based on your server)
effective_cache_size = 1GB                # 75% of available RAM (adjust based on your server)
work_mem = 4MB                            # Memory for sorting and hash operations
maintenance_work_mem = 64MB               # Memory for maintenance operations

# Connection Settings
max_connections = 100                     # Adjust based on your connection pool settings
superuser_reserved_connections = 3

# Write-Ahead Logging (WAL) Configuration
wal_buffers = 16MB
checkpoint_completion_target = 0.9
checkpoint_timeout = 10min
max_wal_size = 1GB
min_wal_size = 80MB

# Query Planner Configuration
random_page_cost = 1.1                    # Lower for SSD storage
effective_io_concurrency = 200            # Higher for SSD storage
seq_page_cost = 1.0

# Logging Configuration
log_min_duration_statement = 1000         # Log queries taking longer than 1 second
log_checkpoints = on
log_connections = off                     # Set to 'on' for debugging
log_disconnections = off                  # Set to 'on' for debugging
log_lock_waits = on
log_temp_files = 10MB

# Autovacuum Configuration (important for performance)
autovacuum = on
autovacuum_max_workers = 3
autovacuum_naptime = 1min
autovacuum_vacuum_threshold = 50
autovacuum_vacuum_scale_factor = 0.2
autovacuum_analyze_threshold = 50
autovacuum_analyze_scale_factor = 0.1

# Background Writer Configuration
bgwriter_delay = 200ms
bgwriter_lru_maxpages = 100
bgwriter_lru_multiplier = 2.0

# Statistics Configuration
track_activities = on
track_counts = on
track_io_timing = on
track_functions = pl

# Locale and Character Set
lc_messages = 'en_US.utf8'
lc_monetary = 'en_US.utf8'
lc_numeric = 'en_US.utf8'
lc_time = 'en_US.utf8'
default_text_search_config = 'pg_catalog.english'

# Timezone
timezone = 'UTC'

# Performance Monitoring
shared_preload_libraries = 'pg_stat_statements'
pg_stat_statements.max = 10000
pg_stat_statements.track = all
